{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/confirmationmodal.tsx", "./src/components/countrycodeselect.tsx", "./src/components/notification.tsx", "./src/components/notificationcontainer.tsx", "./src/components/protectedroute.tsx", "./src/components/sidebar.tsx", "./src/components/table.tsx", "./src/contexts/authcontext.tsx", "./src/contexts/languagecontext.tsx", "./src/contexts/notificationcontext.tsx", "./src/i18n/index.ts", "./src/layouts/mainlayout.tsx", "./src/layouts/userlayout.tsx", "./src/lib/api.ts", "./src/lib/countrycodes.ts", "./src/lib/utils.ts", "./src/pages/auth/login.tsx", "./src/pages/auth/register.tsx", "./src/pages/main/home.tsx", "./src/pages/user/blacklist.tsx", "./src/pages/user/devices.tsx", "./src/pages/user/futuremessages.tsx", "./src/pages/user/groups.tsx", "./src/pages/user/messages.tsx", "./src/pages/user/packages.tsx", "./src/pages/user/sendmessage.tsx", "./src/pages/user/sentmessages.tsx", "./src/pages/user/settings.tsx", "./src/pages/user/waitingmessages.tsx", "./src/pages/user/dashboard.tsx", "./src/pages/user/index.ts", "./src/pages/user/test.tsx", "./src/routes/main.routes.tsx", "./src/routes/routes.types.ts", "./src/routes/user.routes.tsx", "./src/services/currencyservice.ts", "./src/services/purchaseservice.ts"], "version": "5.6.3"}