import { useState, useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import Sidebar from '../components/Sidebar'
import { ProtectedRoute } from '../components/ProtectedRoute'
import { NotificationContainer } from '../components/NotificationContainer'

export function UserLayout() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      if (mobile) {
        setIsSidebarCollapsed(false) // Always expanded on mobile when open
        setIsMobileSidebarOpen(false) // Closed by default on mobile
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const handleSidebarToggle = () => {
    if (isMobile) {
      setIsMobileSidebarOpen(!isMobileSidebarOpen)
    } else {
      setIsSidebarCollapsed(!isSidebarCollapsed)
    }
  }

  const handleMobileSidebarClose = () => {
    if (isMobile) {
      setIsMobileSidebarOpen(false)
    }
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-900 relative">
        {/* Mobile Backdrop */}
        {isMobile && isMobileSidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            onClick={handleMobileSidebarClose}
          />
        )}

        {/* Sidebar */}
        <div className={`
          ${isMobile
            ? `fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out ${
                isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'
              }`
            : 'relative'
          }
        `}>
          <Sidebar
            isCollapsed={isMobile ? false : isSidebarCollapsed}
            setIsCollapsed={handleSidebarToggle}
            isMobile={isMobile}
            isMobileSidebarOpen={isMobileSidebarOpen}
            onMobileClose={handleMobileSidebarClose}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden min-w-0">
          {/* Mobile Header */}
          {isMobile && (
            <div className="bg-gray-900 border-b border-gray-800 p-4 md:hidden">
              <button
                onClick={handleSidebarToggle}
                className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
              >
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          )}

          {/* Main Content */}
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-900 p-4 md:p-6">
            <div className="max-w-full">
              <Outlet />
            </div>
          </main>
        </div>

        {/* Notification Container */}
        <NotificationContainer />
      </div>
    </ProtectedRoute>
  )
}
